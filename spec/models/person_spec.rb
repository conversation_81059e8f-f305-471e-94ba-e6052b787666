require "rails_helper"

require_relative "_uuid_field_behaviour_examples"

RSpec.describe Person do
  let(:account) { FactoryBot.create(:account) }
  let(:email) { Faker::Internet.email }
  let(:employee_id) { "123" }
  let(:date_of_birth) { "1968/02/29" }
  let(:start_date) { "2014/11/28" }

  # Create eagerly so that factories can use timestamps
  # before the clock is frozen
  subject(:person) do
    FactoryBot.create(:person,
      account: account,
      email: email,
      employee_id: employee_id,
      date_of_birth: DateTime.parse(date_of_birth),
      start_date: DateTime.parse(start_date))
  end

  it_behaves_like "a model with uuid and version fields", :aggregate_id, -> { FactoryBot.build(:person) }

  before { Timecop.freeze(Time.utc(2014, 11, 30)) }
  after { Timecop.return }

  describe "#set_demographic_values!" do
    let(:person) { FactoryBot.create(:person) }
    let(:demographic_id) { SecureRandom.uuid }
    let(:demographic_value_id) { SecureRandom.uuid }

    before do
      if previous_demographic_id && previous_demographic_value_id
        doc = build_assignment_doc(previous_demographic_id, previous_demographic_value_id)
        person.set(demographic_value_assignments: [doc])
      end

      person.set_demographic_values!([
        {demographic_id: demographic_id, demographic_value_id: demographic_value_id}
      ])
    end

    context "when there user has no previous demographics" do
      let(:previous_demographic_id) { nil }
      let(:previous_demographic_value_id) { nil }

      it "sets the new demographic" do
        expect(person.demographic_value_assignments.length).to eq 1
        assignment = person.demographic_value_assignments.first
        expect(assignment.demographic_id).to eq demographic_id
        expect(assignment.demographic_value_id).to eq demographic_value_id
      end
    end

    context "when the user has a previous demographic" do
      let(:previous_demographic_id) { SecureRandom.uuid }
      let(:previous_demographic_value_id) { SecureRandom.uuid }

      it "adds the new demographic and removes existing unrelated assignments" do
        expect(person.demographic_value_assignments.length).to eq 1
        assignment = person.demographic_value_assignments.first
        expect(assignment.demographic_id).to eq demographic_id
        expect(assignment.demographic_value_id).to eq demographic_value_id
      end

      context "when updating an existing demographic" do
        let(:demographic_id) { previous_demographic_id }

        context "with a different value" do
          it "updates the assignment for that demographic" do
            expect(person.demographic_value_assignments.length).to eq 1
            assignment = person.demographic_value_assignments.find_by(demographic_id: demographic_id)
            expect(assignment.demographic_value_id).to eq demographic_value_id
          end
        end

        context "with a nil value" do
          let(:demographic_value_id) { nil }

          it "removes the assignment" do
            expect(person.demographic_value_assignments.length).to eq 0
          end
        end

        context "with a blank value" do
          let(:demographic_value_id) { "" }

          it "removes the assignment" do
            expect(person.demographic_value_assignments.length).to eq 0
          end
        end
      end
    end
  end

  describe "Updating Person#updated_at" do
    let(:demographic_id) { SecureRandom.uuid }
    let(:demographic_value_id) { SecureRandom.uuid }
    before { Timecop.return }

    describe "#set_demographic_values!" do
      it "sets the updated_at on person" do
        expect {
          person.set_demographic_values!([
            {demographic_id: demographic_id, demographic_value_id: demographic_value_id}
          ])
        }.to change(person, :updated_at)
        expect(person.updated_at).to be_within(1.second).of(Time.now)
      end
    end

    describe "#add_demographic_values!" do
      it "sets the updated_at on person" do
        expect {
          person.add_demographic_values!([
            {demographic_id: demographic_id, demographic_value_id: demographic_value_id}
          ])
        }.to change(person, :updated_at)
        expect(person.updated_at).to be_within(1.second).of(Time.now)
      end
    end

    describe "#set_demographic_value!" do
      context "when adding a new demographic" do
        it "sets the updated_at on person" do
          expect {
            person.set_demographic_value!(demographic_id, demographic_value_id)
          }.to change(person, :updated_at)
          expect(person.updated_at).to be_within(1.second).of(Time.now)
        end
      end

      context "when removing an existing demographic" do
        before do
          person.set_demographic_value!(demographic_id, demographic_value_id)
        end

        it "sets the updated_at on person" do
          expect {
            person.set_demographic_value!(demographic_id, nil)
          }.to change(person, :updated_at)
          expect(person.updated_at).to be_within(1.second).of(Time.now)
        end
      end
    end

    describe "#update_core_data" do
      it "sets the updated_at on person" do
        expect {
          person.update_core_data
        }.to change(person, :updated_at)
        expect(person.updated_at).to be_within(1.second).of(Time.now)
      end
    end
  end

  describe "#add_demographic_values!" do
    let(:person) { FactoryBot.create(:person) }
    let(:demographic_id) { SecureRandom.uuid }
    let(:demographic_value_id) { SecureRandom.uuid }

    before do
      if previous_demographic_id && previous_demographic_value_id
        doc = build_assignment_doc(previous_demographic_id, previous_demographic_value_id)
        person.set(demographic_value_assignments: [doc])
      end

      person.add_demographic_values!([
        {demographic_id: demographic_id, demographic_value_id: demographic_value_id}
      ])
    end

    context "when there user has no previous demographics" do
      let(:previous_demographic_id) { nil }
      let(:previous_demographic_value_id) { nil }

      it "adds the new demographic" do
        expect(person.demographic_value_assignments.length).to eq 1
        assignment = person.demographic_value_assignments.first
        expect(assignment.demographic_id).to eq demographic_id
        expect(assignment.demographic_value_id).to eq demographic_value_id
      end
    end

    context "when the user has a previous demographic" do
      let(:previous_demographic_id) { SecureRandom.uuid }
      let(:previous_demographic_value_id) { SecureRandom.uuid }

      it "adds the new demographic without removing existing unrelated assignments" do
        expect(person.demographic_value_assignments.length).to eq 2
        assignment = person.demographic_value_assignments.find_by(demographic_id: demographic_id)
        expect(assignment.demographic_value_id).to eq demographic_value_id
      end

      context "when updating an existing demographic" do
        let(:demographic_id) { previous_demographic_id }

        context "with a different value" do
          it "updates the assignment for that demographic" do
            expect(person.demographic_value_assignments.length).to eq 1
            assignment = person.demographic_value_assignments.find_by(demographic_id: demographic_id)
            expect(assignment.demographic_value_id).to eq demographic_value_id
          end
        end

        context "with a nil value" do
          let(:demographic_value_id) { nil }

          it "removes the assignment" do
            expect(person.demographic_value_assignments.length).to eq 0
          end
        end

        context "with a blank value" do
          let(:demographic_value_id) { "" }

          it "removes the assignment" do
            expect(person.demographic_value_assignments.length).to eq 0
          end
        end
      end
    end
  end

  describe "#remove_demographic_values!" do
    it "removes the demographic values from the assignments array" do
      person = FactoryBot.create(:person)
      demographic_id_1 = SecureRandom.uuid
      demographic_value_id_1 = SecureRandom.uuid
      demographic_id_2 = SecureRandom.uuid
      demographic_value_id_2 = SecureRandom.uuid
      demographic_value_assignments_doc_1 = build_assignment_doc(demographic_id_1, demographic_value_id_1)
      demographic_value_assignments_doc_2 = build_assignment_doc(demographic_id_2, demographic_value_id_2)
      person.set(demographic_value_assignments: [demographic_value_assignments_doc_1, demographic_value_assignments_doc_2])

      person.remove_demographic_assignments(demographic_id_1, [demographic_value_id_1])

      expect(person.demographic_value_assignments.length).to eq 1
      assignment = person.demographic_value_assignments.first
      expect(assignment.demographic_id).to eq demographic_id_2
      expect(assignment.demographic_value_id).to eq demographic_value_id_2
    end
  end

  describe "#has_valid_identifier?" do
    shared_examples "a person with a valid identifier" do
      it { is_expected.to have_valid_identifier }

      it "adds no errors" do
        person.has_valid_identifier?
        expect(person.errors).to be_blank
      end
    end

    context "when there is an email and employee_id" do
      it_behaves_like "a person with a valid identifier"
    end

    context "when there is no email" do
      let(:email) { "" }

      it_behaves_like "a person with a valid identifier"
    end

    context "when there is no employee_id" do
      let(:employee_id) { "" }

      it { is_expected.to have_valid_identifier }

      it_behaves_like "a person with a valid identifier"
    end

    context "when there is no email or employee_id" do
      let(:email) { "" }
      let(:employee_id) { "" }

      it { is_expected.to_not have_valid_identifier }

      it "adds an error" do
        person.has_valid_identifier?
        expect(person.errors).to be_present
      end
    end
  end

  describe "#age" do
    context "for a person born in a leap year" do
      it "returns the right age for the person" do
        expect(person.age).to eq(46)
      end
    end

    context "on the birthday" do
      let(:date_of_birth) { "1983/11/30" }
      it "counts the current year" do
        expect(person.age).to eq(31)
      end
    end

    context "when the birthday has not passed yet" do
      let(:date_of_birth) { "1968/12/29" }
      it "returns the right age for the person" do
        expect(person.age).to eq(45)
      end
    end
  end

  describe "#tenure_in_months" do
    context "when the start date is in the same month" do
      it "returns nil" do
        expect(person.tenure_in_months).to eq(0)
      end
    end

    context "when we are on the start date anniversary" do
      let(:start_date) { "2012/11/30" }
      it "counts the current month" do
        expect(person.tenure_in_months).to eq(24)
      end
    end

    context "when the start date is in a leap year" do
      let(:start_date) { "2012/02/29" }
      it "returns the correct tenure" do
        expect(person.tenure_in_months).to eq(33)
      end
    end
  end

  describe "#email_or_name" do
    let(:name) { "Fiona" }
    subject { Person.new(name: name, email: email).email_or_name }

    context "when email is present" do
      let(:email) { "<EMAIL>" }

      it { is_expected.to eq email }
    end

    context "when email is blank" do
      let(:email) { "" }

      it { is_expected.to eq name }
    end

    context "when email is nil" do
      let(:email) { nil }

      it { is_expected.to eq name }
    end
  end

  describe "#emailable?" do
    subject { person }

    context "when email is present" do
      let(:email) { "<EMAIL>" }

      it { is_expected.to be_emailable }
    end

    context "when email is blank" do
      let(:email) { "" }

      it { is_expected.to_not be_emailable }
    end

    context "when email is nil" do
      let(:email) { nil }

      it { is_expected.to_not be_emailable }
    end
  end

  describe "scope.with_locked_names" do
    subject { described_class.with_locked_names }
    let(:person_with_locked_name) { FactoryBot.create(:person, name_locked: true) }
    let(:person_without_locked_name) { FactoryBot.create(:person, name_locked: false) }

    context "when filtering by name_locked" do
      it { is_expected.to eq [person_with_locked_name] }
    end
  end

  describe "#demographic_value" do
    let(:demographic_id) { SecureRandom.uuid }
    let(:demographic_value_id) { SecureRandom.uuid }

    context "when the demographic value assignment exists" do
      before(:each) do
        person.demographic_value_assignments << DemographicValueAssignment.new(
          demographic_id: demographic_id,
          demographic_value_id: demographic_value_id
        )
      end

      it "returns the value" do
        expect(person.demographic_value(demographic_id)).to eq demographic_value_id
      end
    end

    context "when the demographic value assignment does not exist" do
      it "returns nil" do
        expect(person.demographic_value(demographic_id)).to be_nil
      end
    end
  end

  describe "#set_demographic_value!" do
    let(:demographic_id) { SecureRandom.uuid }
    let(:demographic_value_id) { SecureRandom.uuid }

    context "when the demographic value assignment exists" do
      before(:each) do
        person.demographic_value_assignments << DemographicValueAssignment.new(
          demographic_id: demographic_id,
          demographic_value_id: demographic_value_id
        )
      end

      context "and the demographic value id is nil" do
        it "deletes the assignment" do
          new_demographic_value_id = nil
          person.set_demographic_value!(demographic_id, new_demographic_value_id)
          expect(person.demographic_value(demographic_id)).to eq new_demographic_value_id
        end
      end

      context "and the demographic value id is present" do
        it "updates the assignment" do
          new_demographic_value_id = SecureRandom.uuid
          person.set_demographic_value!(demographic_id, new_demographic_value_id)
          expect(person.demographic_value(demographic_id)).to eq new_demographic_value_id
        end
      end
    end

    context "when the demographic value assignment does not exist" do
      context "and the demographic value id is present" do
        it "creates the assignment" do
          expect {
            person.set_demographic_value!(demographic_id, demographic_value_id)
          }.to change(person.demographic_value_assignments, :count).by 1
        end
      end

      context "and the demographic value id is nil" do
        let(:demographic_value_id) { nil }
        it "does not create an assignment" do
          expect {
            person.set_demographic_value!(demographic_id, demographic_value_id)
          }.not_to change(person.demographic_value_assignments, :count)
        end
      end

      context "and the demographic value id is blank" do
        let(:demographic_value_id) { "" }
        it "does not create an assignment" do
          expect {
            person.set_demographic_value!(demographic_id, demographic_value_id)
          }.not_to change(person.demographic_value_assignments, :count)
        end
      end
    end
  end

  describe "#find_employee_by_employee_id" do
    let(:account) { FactoryBot.create(:account) }
    let(:employee) { FactoryBot.create(:person, account: account) }

    it "finds the employee" do
      result = Person.find_employee_by_employee_id(account.subdomain, employee.employee_id)
      expect(result).to eq(employee)
    end
  end

  describe "#find_employees_by_identifier" do
    let(:employee) { FactoryBot.create(:person) }

    it "finds the employees by email" do
      expect(Person.find_employees_by_identifier(employee.email).to_a).to eq([employee])
    end

    it "finds the employee by object id" do
      expect(Person.find_employees_by_identifier(employee.id.to_s).to_a).to eq([employee])
    end

    context "when multiple users are present for an email" do
      let(:employee2) { FactoryBot.create(:person) }
      it "returns correct count" do
        employee2.email = employee.email
        employee2.save!(validate: false)
        expect(Person.find_employees_by_identifier(employee.email).to_a).to match_array([employee, employee2])
      end
    end
  end

  describe "#find_employee_by_identifier_and_subdomain" do
    let(:account) { FactoryBot.create(:account) }
    let(:employee) { FactoryBot.create(:person, account: account) }

    it "finds the employee by email" do
      expect(Person.find_employee_by_identifier_and_subdomain(employee.email, account.subdomain)).to eq(employee)
    end

    it "finds the employee by object id" do
      expect(Person.find_employee_by_identifier_and_subdomain(employee.id.to_s, account.subdomain)).to eq(employee)
    end
  end

  describe "#find_employee_by_name" do
    let!(:other_employee) { FactoryBot.create(:person, name: "Bruce Banner") }
    let!(:employee) { FactoryBot.create(:person, name: "Bruce Wayne") }

    it "finds the employee by a substring of their name" do
      expect(Person.find_employee_by_name("ce wa")).to eq(employee)
    end
  end

  describe "#display_name" do
    context "when person has a preferred name" do
      let(:person) { Person.new(name: "Bruce", preferred_name: "Batman") }

      it "returns preferred name" do
        expect(person.display_name).to eq(person.preferred_name)
      end
    end

    context "when preferred_name is nil" do
      let(:person) { Person.new(name: "Bruce", preferred_name: nil) }

      it "returns name" do
        expect(person.display_name).to eq(person.name)
      end
    end

    context "when preferred_name is blank" do
      let(:person) { Person.new(name: "Bruce", preferred_name: "") }

      it "returns name" do
        expect(person.display_name).to eq(person.name)
      end
    end
  end

  describe "#name_locked" do
    context "when lock is set to true" do
      let(:person) { Person.new(name: "Bruce", name_locked: true) }

      it "returns true" do
        expect(person.name_locked).to eq(true)
      end
    end

    context "when lock is never set" do
      let(:person) { Person.new(name: "Bruce", name_locked: nil) }

      it "returns false" do
        expect(person.name_locked).to eq(false)
      end
    end

    context "when lock is never set (with unset)" do
      let(:person) { Person.new(name: "Bruce") }

      before { person.unset(:name_locked) }

      it "returns false" do
        expect(person.name_locked).to eq(false)
      end
    end
  end

  describe "::email" do
    it "returns nil when the email is nil" do
      expect(Person.email(nil)).to eq nil
    end

    it "returns nil when the email is an empty string" do
      Person.create!(email: "", employee_id: "123456", name: "Test User", password: "foobarbaz")
      expect(Person.email("")).to eq nil
    end

    it "returns nil when the email is a string with whitespace" do
      Person.create!(email: "", employee_id: "123456", name: "Test User", password: "foobarbaz")
      expect(Person.email("                ")).to eq nil
    end

    it "returns nil when the email is not found" do
      expect(Person.email("<EMAIL>")).to eq nil
    end

    it "returns the person when the email is found" do
      person = Person.create!(email: "<EMAIL>", employee_id: "123456", name: "Test User", password: "foobarbaz")
      expect(Person.email(person.email)).to eq person
    end
  end

  describe "email uniqueness validation for persons across accounts" do
    let(:account1) { FactoryBot.create(:account) }
    let(:account2) { FactoryBot.create(:account) }
    let(:person1) do
      FactoryBot.create(
        :person,
        name: "person1",
        account: account1,
        email: "<EMAIL>"
      )
    end
    let(:person2) do
      FactoryBot.create(
        :person,
        name: "person2",
        account: account2,
        email: "<EMAIL>"
      )
    end
    let(:person3) do
      FactoryBot.create(
        :person,
        name: "person3",
        account: account1
      )
    end

    it "allows persons in different accounts to have the same email" do
      person2.email = person1.email
      person2.save
      expect(person2).to be_valid
    end

    it "does not allow persons in the same account to have the same email" do
      person3.email = person1.email
      person3.save
      expect(person3.errors[:email]).to include("is already taken by another user in this account")
    end

    it "validates email uniqueness case-insensitively within the same account" do
      person3.email = person1.email.upcase
      person3.save
      expect(person3.errors[:email]).to include("is already taken by another user in this account")
    end
  end

  def build_assignment_doc(demographic_id, demographic_value_id)
    DemographicValueAssignment.build_doc(demographic_id, demographic_value_id)
  end
end
