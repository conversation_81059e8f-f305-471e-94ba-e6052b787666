require "rails_helper"

RSpec.describe Find<PERSON>ersonHelper do
  let(:find_person_helper_class) { Class.new { extend Find<PERSON><PERSON>Helper } }
  let(:account_id) { BSON::ObjectId.new }

  describe "#find_person" do
    let(:employee_aggregate_id) { "employee-123" }
    let(:select_option) { double("SelectOption", employee_aggregate_id: employee_aggregate_id, value: "<EMAIL>") }

    context "when a User is found by aggregate_id" do
      let(:user) { double("User") }

      before do
        allow(User).to receive(:where).with(aggregate_id: employee_aggregate_id).and_return(double(first: user))
      end

      it "returns the User" do
        expect(find_person_helper_class.find_person(select_option: select_option, account_id: account_id)).to eq(user)
      end
    end

    context "when no User by aggregate_id is found" do
      before do
        allow(User).to receive(:where).with(aggregate_id: employee_aggregate_id).and_return(double(first: nil))
      end

      context "and a Person is found by email" do
        let(:person) { double("Person") }

        before do
          allow(Person).to receive(:where).with(email: "<EMAIL>", account_id: account_id).and_return(double(first: person))
        end

        it "returns the Person" do
          expect(find_person_helper_class.find_person(select_option: select_option, account_id: account_id)).to eq(person)
        end
      end

      context "when no User by aggregate_id or Person by email is found" do
        before do
          allow(Person).to receive(:where).with(email: "<EMAIL>", account_id: account_id).and_return(double(first: nil))
        end

        context "and a Person is found by employee_id" do
          let(:person) { double("Person") }

          before do
            allow(Person).to receive(:where).with(employee_id: "<EMAIL>", account_id: account_id).and_return(double(first: person))
          end

          it "returns the Person" do
            expect(find_person_helper_class.find_person(select_option: select_option, account_id: account_id)).to eq(person)
          end
        end

        context "and no Person is found by employee_id" do
          before do
            allow(Person).to receive(:where).with(employee_id: "<EMAIL>", account_id: account_id).and_return(double(first: nil))
          end

          it "returns nil" do
            expect(find_person_helper_class.find_person(select_option: select_option, account_id: account_id)).to be_nil
          end
        end
      end
    end
  end

  describe "#find_persons_by_aggregate_ids" do
    let(:aggregate_ids) { ["id1", "id2"] }
    let(:users) { [double("User1"), double("User2")] }

    before do
      allow(User).to receive(:in).with(aggregate_id: aggregate_ids).and_return(double(only: users))
    end

    it "returns users found by aggregate_ids" do
      expect(find_person_helper_class.find_persons_by_aggregate_ids(aggregate_ids: aggregate_ids)).to eq(users)
    end
  end
end
