class Admin::MasqueradesController < SuperuserController
  component :admin
  include VerifySameOrigin
  include JwtAuthentication
  include <PERSON><PERSON><PERSON><PERSON>per
  include Authentication::FusionAuthHelper

  # Add a before action to check for masquerading timeout
  before_action :check_masquerading_timeout

  MASQUERADE_VALIDATION_ERRORS = {
    missing_reason: "Sorry, you cannot masquerade without providing a reason.",
    restricted: "Sorry, you don't have the required privileges to masquerade as a user from this account.",
    inactive_user: "Sorry, you cannot masquerade as a deleted user.",
    another_superuser: "Sorry, you cannot masquerade as another superuser.",
    same_account: "Sorry, you cannot masquerade as another user in the same account."
  }

  # Overrides the access control in SuperuserController so that masquerade sessions can be destroyed
  allows_access_to :superusers,
    except: {destroy: :all_authenticated_users}

  # The "Stop Masquerading" link needs to work from a global navbar (not just inside murmur).
  # This means it cannot support standard CSRF token validation, so we simply validate the same origin instead (similar to CORS).
  replace_csrf_with_origin_check(only: [:destroy])

  def new
    stop_masquerading
  end

  # Assume the identify of any user in the system.
  # This will mean that whenever current_user is called, the assumed identify will be returned instead of the logged in user.
  # Users are identified only by email or BSON::ObjectId as these are the only two user fields guaranteed to be unique system wide.
  def create
    @subdomain = params[:subdomain]
    @employee_id = params[:employee_id]
    @identifier = params[:identifier]
    @name = params[:name]
    @reason = if Rails.env.development? && params[:reason].blank?
      "development"
    else
      params[:reason]
    end

    masqueraded_user = MasqueradeUserFinder.new(devise_current_user, @subdomain, @employee_id, @identifier, @name).find! { |error_message|
      flash[:error] = error_message
      return render "new"
    }

    Policies::Masquerade.validate!(devise_current_user, masqueraded_user, @reason) do |error_key|
      flash[:error] = MASQUERADE_VALIDATION_ERRORS.fetch(error_key)
      return render "new"
    end

    if fusionauth_jwt_post_signin_enabled?(get_subdomain_from_request)
      begin
        create_fusionauth_entity(masqueraded_user)
        response.headers["X-CA-FA-JWT-Refresh"] = true
      rescue => e
        Rails.logger.error("Failed to create FusionAuth entity for masquerade session: #{e.message}")
        Sentry.capture_exception(e)
        render_error
        return
      end
    else
      masquerade_as_user!(masqueraded_user)

      # Store the end time for masquerading
      session[:masquerade_end_time] = 12.hours.from_now
    end

    # Store original superuser's info in another cookie
    original_user_info = {
      locale: devise_current_user.locale,
      employee_aggregate_id: devise_current_user.aggregate_id,
      account_aggregate_id: devise_current_user.account.aggregate_id
    }.to_json

    cookies[:original_user_info] = {
      value: ::Base64.strict_encode64(original_user_info),
      httponly: false,
      path: "/",
      same_site: :lax,
      max_age: 6.days,
      secure: Rails.env.production? || Rails.env.staging?
    }

    # Set both cookies for masqueraded user
    set_user_data_cookie_by_region(user_info(masqueraded_user))
    set_basic_user_cookie(user_info(masqueraded_user))

    redirect_to(surveys_path)
  end

  # Resume the logged in user's normal identify. Superhero days are over.
  def destroy
    stop_masquerading

    unless cookies[:original_user_info].nil?
      # Get original user info
      original_user_info = JSON.parse(::Base64.strict_decode64(cookies[:original_user_info])).to_json

      # Set both cookies back to original user
      set_user_data_cookie_by_region(original_user_info)
      set_basic_user_cookie_from_original_info(original_user_info)

      # Delete the original user info cookie
      cookies.delete(:original_user_info)
    end

    redirect_to new_masquerade_path
  end

  private

  def get_jwt_from_request_header
    if fusionauth_jwt_post_signin_enabled?(get_subdomain_from_request)
      request.headers.fetch("X-CA-FA-Authorization", nil)
    else
      request.headers.fetch("X-CA-SGW-Authorization", nil).presence ||
        request.headers.fetch("Authorization", nil).presence
    end
  end

  def set_basic_user_cookie(user_info)
    cookies["cultureamp.user-data"] = {
      value: ::Base64.strict_encode64(user_info),
      httponly: false,
      path: "/",
      same_site: :lax,
      max_age: 6.days,
      secure: Rails.env.production? || Rails.env.staging?
    }
  end

  def set_basic_user_cookie_from_original_info(user_info)
    cookies["cultureamp.user-data"] = {
      value: ::Base64.strict_encode64(user_info),
      httponly: false,
      path: "/",
      same_site: :lax,
      max_age: 6.days,
      secure: Rails.env.production? || Rails.env.staging?
    }
  end

  def create_fusionauth_entity(masqueraded_user)
    search_entity_type = Authentication::Commands::FusionAuthSearchEntityType.new
    create_entity = Authentication::Commands::FusionAuthCreateEntity.new

    entity_type_result = search_entity_type.call(name: "User Session")
    unless entity_type_result.success?
      Rails.logger.error("Failed to find User Session entity type")
      raise "Failed to find User Session entity type"
    end

    entity_type_id = entity_type_result.value!
    create_result = create_entity.call(
      entity_type_id: entity_type_id,
      session_id: jwt_sid,
      masquerade_effective_user_id: masqueraded_user.aggregate_id,
      real_user_id: jwt_real_user_id,
      account_id: jwt_account_id,
      masquerade_account_id: masqueraded_user.account.aggregate_id
    )

    unless create_result.success?
      Rails.logger.error("Failed to create FusionAuth entity for masquerade session")
      Sentry.capture_exception(create_result.failure)
      raise "Failed to create FusionAuth entity for masquerade session"
    end
  end

  def check_masquerading_timeout
    if session[:masquerade_end_time] && Time.now > session[:masquerade_end_time]
      stop_masquerading
      redirect_to new_masquerade_path, alert: "Masquerading session has ended due to time limit."
    end
  end

  def masquerade_as_user!(masqueraded_user)
    Rails.logger.info "User \"#{devise_current_user}\" has started masquerading as \"#{masqueraded_user}\"."
    RestrictedActionLog.log!(action: :masquerade, reason: @reason, camper: devise_current_user, account: masqueraded_user.account, user: masqueraded_user)
    session[:masquerade_user] = [masqueraded_user.id.to_s, masqueraded_user.generate_masquerade_token]
    masqueraded_user.save! # Save the newly generated masquerade token
  end

  class MasqueradeUserFinder
    def initialize(camper, subdomain, employee_id, identifier, name)
      @camper = camper
      @subdomain = subdomain
      @employee_id = employee_id
      @identifier = identifier
      @name = name
    end

    def find!
      failure_message = nil
      if @subdomain.present? && @employee_id.present?
        user = Person.find_employee_by_employee_id(@subdomain, @employee_id)
        failure_message = "Sorry, couldn't find a user with subdomain '#{@subdomain}' and employee_id '#{@employee_id}'" unless user
      elsif @identifier.present? && @subdomain.present?
        user = Person.find_employee_by_identifier_and_subdomain(@identifier, @subdomain)
        failure_message = "Sorry, couldn't find a user with subdomain '#{@subdomain}' and email or ObjectId '#{@identifier}'" unless user
      elsif @identifier.present?
        employees = Person.find_employees_by_identifier(@identifier)
        if employees.count > 1
          user = nil
          failure_message = "Sorry, there are multiple users with email '#{@identifier}'. Please provide a subdomain."
        else
          user = employees.first_by_id
          failure_message = "Sorry, couldn't find a user with email or ObjectId '#{@identifier}'" unless user
        end
      elsif @name.present?
        user = Person.find_employee_by_name(@name, @camper.account)
        failure_message = "Sorry, couldn't find a user with a name that contains '#{@name}'" unless user
      else
        user = nil
        failure_message = "Sorry, we need more information about the user you're masquerading as"
      end

      yield failure_message if failure_message.present?
      user
    end
  end
end
