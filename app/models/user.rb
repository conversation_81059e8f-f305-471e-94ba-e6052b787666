require_dependency "permission"

# See https://bitbucket.org/cultureamp/murmur/issue/926/userrb-occassionally-refers-to-account
# User occassionally refers to Account, which is a Person concept.
class User
  include Mongoid::Document
  include Mongoid::Timestamps
  include Configurable
  include Flaggable
  include Mongoid::Attributes::Dynamic

  VALID_EMAIL_FORMAT = /(\A\z|\A([^@\s]+)@((?:[-a-z0-9_]+\.)+[a-z]{2,})\Z)/i
  VALID_EMAIL_LENGTH = 254

  # Include default devise modules. Others available are:
  # :token_authenticatable, :encryptable, :confirmable, :lockable and :omniauthable
  devise :database_authenticatable, :lockable, :registerable,
    :recoverable, :trackable, :validatable, :timeoutable
  devise :omniauthable, omniauth_providers: [:google_oauth2]
  extend DeviseUserOverrides

  field :v, default: 0 # Schema Version

  # Sign in details
  field :email, type: String
  field :email_bounce, type: Time
  field :email_bounce_cleared, type: Time
  field :email_bounce_type, type: String

  field :encrypted_password

  ## Recoverable
  field :reset_password_token, type: String
  field :reset_password_sent_at, type: Time

  ## Trackable
  field :sign_in_count, type: Integer, default: 0
  field :current_sign_in_at, type: Time
  field :last_sign_in_at, type: Time
  field :current_sign_in_ip, type: String
  field :last_sign_in_ip, type: String
  field :onboarding_expiration_at, type: Time, default: nil

  ## Lockable
  field :failed_attempts
  field :locked_at
  field :unlock_token

  field :masquerade_token, type: String

  # Used by the dismissible_blocks gem.
  # Stores which help blocks have been dismissed.
  # https://github.com/pbougie/dismissible_blocks
  field :dismissed_blocks, type: Array, default: []

  # For newly invited admins that have not set their password yet
  field :invited_as_new_admin, type: Boolean, default: false

  # User migrated to new Tenancy model in FusionAuth
  field :migrated_to_multi_tenant_fusionauth, type: Boolean, default: false

  scope :email_bounced, -> { gte(email_bounce: Time.at(0)) }

  validates :email, uniqueness: {scope: :account_id, case_sensitive: true, allow_blank: true}
  validates :email, format: {with: VALID_EMAIL_FORMAT, allow_nil: true}
  validates :email, length: {maximum: VALID_EMAIL_LENGTH}
  validates_strength_of :password, with: :email, allow_nil: true, using: CustomPasswordTester
  validates :email_bounce_type, inclusion: {in: %w[soft hard], allow_nil: true}

  index({unlock_token: 1}, {background: true})
  index({masquerade_token: 1}, {background: true})
  # This index was recommended by MongoSupport however after adding this we saw significant slowdown
  # It was removed manually in #_incident-205.
  # index({account_id: 1, association_type: 1, active_until: 1, _type: 1, email: 1, _id: 1})

  def first_sign_in?
    sign_in_count < 2
  end

  def clear_email_bounce
    self.email_bounce = nil
    self.email_bounce_cleared = Time.now.utc
    self
  end

  def get_reset_password_token
    @token
  end

  def dismiss_block(block)
    dismissed_blocks.push(block)
    save!
  end

  def reset_token
    # Note: Devise now clears the reset password token automatically when you change the password.
    # We used to randomise the password here, but that caused the reset token to be lost.
    @token, enc = Devise.token_generator.generate(self.class, :reset_password_token)
    self.reset_password_token = enc
    self.reset_password_sent_at = Time.now.utc
    save!
    @token
  end

  def invited?
    !reset_password_sent_at.nil? || sign_in_count > 0
  end

  def notify_new_account
    reset_token if sign_in_count == 0
    publish(Topics::NOTIFY_NEW_ACCOUNT)
  end

  def send_reset_password_instructions
    token = set_reset_password_token
    ResetPasswordMailer.delay.reset_password(self, token)

    token
  end

  def send_unlock_instructions
    # Send an analytics event when the user has been sent an account unlock email
    # after too many login attempts.
    Jobs::AnalyticsJob.enqueue(
      account_aggregate_id: account.aggregate_id,
      user_aggregate_id: aggregate_id,
      event_type: Analytics::EVENT_TYPE_PLATFORM_COMMUNICATION_RECEIVED,
      event_properties: {
        Analytics::EVENT_PROPERTY_MEDIUM => Analytics::MEDIUM_EMAIL,
        Analytics::EVENT_PROPERTY_COMMUNICATION_NAME => Analytics::COMMUNICATION_NAME_UNLOCK_ACCOUNT,
        Analytics::EVENT_PROPERTY_SET_PASSWORD_TOKEN => true
      }
    )

    super
  end

  def invite_to_app
    # We don't call reset_token here because @token never appears to persist to the
    # mailer, i.e. calling get_reset_password_token within the mailer returns an empty
    # value, even if a reset token is generated.
    AdminDrivenInviteMailer.delay.invite(self)
  end

  # overrides the definition in Devise::Models::Validatable
  def email_required?
    false
  end

  def generate_masquerade_token
    self.masquerade_token ||= self.class.generate_masquerade_token
  end

  # Returns true if email_bounce was set, false otherwise
  def process_email_bounce!(bounced_at:, bounce_type:)
    return false if email_bounce_cleared && email_bounce_cleared >= bounced_at

    self.email_bounce = bounced_at
    self.email_bounce_type = bounce_type
    save(validate: false)
    true
  end

  def has_live_support?
    account.live_support_enabled?
  end

  # Called in a Warden hook (devise.rb) and ApplicationController#sign_in so that the value is available for analytics after sign in
  def cache_failed_attempts
    @cached_failed_attempts = failed_attempts
  end

  attr_reader :cached_failed_attempts

  # -- Class Methods
  class << self
    def generate_masquerade_token
      generate_token(:masquerade_token)
    end

    # TODO: // this is a hack to enable the customer survey to be run as we can't have the same email address in more than 1 account
    def customer_survey_check(email)
      email&.gsub("+customer_survey", "")
    end

    def generate_token(column)
      loop do
        token = Devise.friendly_token
        break token unless to_adapter.find_first(column => token)
      end
    end

    def process_soft_bounce(bounces:)
      process_email_bounces(bounces: bounces, type: "soft")
    end

    def process_hard_bounce(bounces:)
      process_email_bounces(bounces: bounces, type: "hard")
    end

    # Input argument would something be an array of SendGridBounce objects
    # class SendGridBounce < Value.new(:email, :bounced_at)
    # This returns 2 arrays, one contains emails that was skipped, another contains emails that was flagged
    def process_email_bounces(bounces:, type:)
      skipped_emails = []
      flagged_emails = []

      all_emails = bounces.map(&:email)
      valid_users = User.in(email: all_emails)
      skipped_emails = all_emails - valid_users.pluck(:email)

      valid_users.each do |user|
        bounce = bounces.detect { |bounce| bounce.email == user.email }
        bounced_email = bounce.email

        if user.process_email_bounce!(bounced_at: bounce.bounced_at, bounce_type: type)
          flagged_emails << bounced_email
        else
          skipped_emails << bounced_email
        end
      end

      [skipped_emails, flagged_emails]
    end
  end

  # -- TO BE MIGRATED OFF USER:

  # TODO: - Remove once we've introduced Employments. We should be counting how many current 'Employments' are in place, not storing this flag on people.
  field :is_surveyed, type: Boolean, default: true

  belongs_to :manager, class_name: "User", inverse_of: :subordinates

  has_one :reporting_api_token, class_name: "ReportingApi::Token"

  has_many :owns, class_name: "Survey", inverse_of: :owner
  has_many :survey_participants
  has_many :responses
  has_many :responses_as_reviewer, class_name: "Response", inverse_of: :reviewer
  has_many :survey_topics_as_subject, class_name: "SurveyTopic", inverse_of: :subject
  has_many :subordinates, class_name: "User", inverse_of: :manager
  has_many :restricted_actions_performed, inverse_of: :camper, class_name: "RestrictedActionLog"
  has_many :restricted_actions_undergone, inverse_of: :user, class_name: "RestrictedActionLog"
  has_many :experiment_memberships
  has_many :user_focuses, class_name: "UserFocus", dependent: :destroy

  has_and_belongs_to_many :user_types, inverse_of: nil

  # Permissions that have been granted to this user.
  embeds_many :grant_permissions

  # @return [ Array ] list of responses that are still open and valid for a given
  # user.
  def active_responses
    candidates = responses.select { |r| r.survey.active? }
    candidates.select { |r| r.survey.lifecycle? ? r.status == :issued : r.status != :submitted }
  end

  def responses_as_lifecycle_participant
    Response.or({user: self}, {reviewer: self})
  end

  # @return [ Array ] list of responses that are associated with active surveys,
  # irrespective of status. If there are multiple for a given survey, the last
  # is selected.
  def potential_responses
    # Get the list of surveys
    surveys = responses.map(&:survey)
      .reject(&:nil?)
      .select { |s| s.active? && !s.master? }
      .uniq

    # Get the last response for each survey
    surveys.map { |s| responses.survey(s).order_by(:created_at.desc).first }
  end

  def permission_sets
    valid_grant_permissions.reduce([]) { |result, gp| result + gp.permission_sets.active }
  end

  def valid_grant_option_permissions_for_survey(survey)
    grant_permissions.where("_type" => GrantOptionPermission, :survey => survey).all
  end

  def valid_grant_permissions(opts = {})
    origin = opts[:origin]
    grant_permissions.select { |gp| (origin.nil? || gp.origin.to_s == origin.to_s) && gp.valid_at?(opts[:valid_at]) }
  end

  def first_grant_for_survey(survey, permission_class, grant_class = nil)
    valid_grant_permissions.detect { |g| g.is_a?(GrantSurveyPermission) && g.can_access_survey?(survey, permission_class) && (grant_class.nil? || g.is_a?(grant_class)) }
  end

  def first_survey_grant_for_survey(survey, permission_class)
    valid_grant_permissions.detect { |g| g._type == GrantSurveyPermission.name && g.can_access_survey?(survey, permission_class) }
  end

  def can_access_account?(account, permission_class)
    valid_grant_permissions.detect { |gp| gp.can_access_account?(account, permission_class) }.present?
  end

  def valid_account_permission_grants(account, permission_class)
    valid_grant_permissions.select { |gp| gp.can_access_account?(account, permission_class) }
  end

  def valid_survey_permission_grants(survey, permission_class)
    valid_grant_permissions.select { |gp| gp.can_access_survey?(survey, permission_class) }
  end

  def valid_option_permission_grants(survey, question, permission_class)
    valid_grant_permissions.select { |g| g.is_a?(GrantOptionPermission) && g.question_id == question.id && g.can_access_survey?(survey, permission_class) }
  end

  def get_dissect_options(survey, report_access_grant_or_permission_class, report_filters)
    case report_access_grant_or_permission_class
    when DemoReportAccessGrant, ReportAccessGrant, AdministratorReportAccessGrant, ActionDashboardReportAccessGrant
      [] # Unused in surveys with Report Sharing enabled
    else
      legacy_get_dissect_options(survey, report_access_grant_or_permission_class, report_filters)
    end
  end

  def legacy_get_dissect_options(survey, permission_class, _report_filters)
    valid_gps = valid_survey_permission_grants(survey, permission_class)
    valid_gps.reduce([]) { |result, gp| result | gp.required_filters }
  end

  # Returns the subset of filters that are valid
  def validate_dissect_filters(survey, report_access_grant_or_permission_class, report_filters)
    case report_access_grant_or_permission_class
    when DemoReportAccessGrant, ReportAccessGrant, AdministratorReportAccessGrant, ActionDashboardReportAccessGrant
      report_access_grant_or_permission_class.adjust_report_filters(report_filters)
    else
      legacy_validate_dissect_filters(survey, report_access_grant_or_permission_class, report_filters)
    end
  end

  def legacy_validate_dissect_filters(survey, permission_class, report_filters)
    if valid_dissect_filters?(survey, permission_class, report_filters)
      report_filters
    else
      valid_gps = valid_survey_permission_grants(survey, permission_class).group_by(&:priority).values.flatten
      valid_gp = valid_gps.detect { |gp| !gp.is_a?(GrantOptionPermission) } # Ie, a survey, account, or superuser permission
      if valid_gp.nil?
        valid_gp = valid_gps.delete_at(0)

        if valid_gp.is_a?(GrantOptionPermission) && valid_gp.hierarchical?
          stq = survey.survey_to_questions.detect { |stq| stq.question_id == valid_gp.question_id }
          if stq.present?
            # See if we can find the highest scope in the hierarchy.
            valid_gps.each do |gp|
              valid_gp = gp if gp.question_id == valid_gp.question_id && stq.hierarchy.ancestor?(valid_gp.select_option_id, gp.select_option_id)
            end
          end
        end
      end

      if valid_gp.present?
        valid_gp.adjust_report_filters(survey, permission_class, report_filters)
      else
        []
      end
    end
  end

  # -- Permissions
  def can_edit?(user)
    user.can_administer_account?(account)
  end

  def self.valid_email?(email)
    email.present? && email.length < VALID_EMAIL_LENGTH && VALID_EMAIL_FORMAT =~ email
  end

  def user_id
    id
  end

  def add_type(type)
    return unless type
    return if user_types.include?(type)

    user_types << type
    save
  end

  def to_summary
    {
      id: id.to_s,
      aggregate_id: aggregate_id.to_s,
      name: name,
      email: email
    }
  end
end

class SendGridBounce < Value.new(:email, :bounced_at)
end
