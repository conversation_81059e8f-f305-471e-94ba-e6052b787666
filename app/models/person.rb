class Person < User
  include Mongoid::Document
  include Mongoid::Timestamps
  include Mongoid::Attributes::Dynamic
  extend UuidFieldBehaviour

  add_uuid_and_version :aggregate_id

  # Person Roles
  include Superuser
  include Associate
  include Administrator
  include ReportConsumer
  include IndividualEffectivenessParticipant

  include Slackable

  # We hold a restriction that a User can only belong to one account.
  # The assumption is that even external consultants with access to multiple accounts would still first log in to their own account in order to see those other accounts.
  belongs_to :account, index: true

  embeds_many :demographic_value_assignments

  has_one :profile_image, dependent: :destroy

  # -- Identifiers
  field :name, type: String
  field :name_locked, default: false # Prevents automatic updates to the name field. `name_locked` should only be exposed to employee data admin pages.
  field :preferred_name, type: String
  field :employee_id, type: String
  field :previous_email, type: String # Note, person inherits email from User.
  field :previous_employee_id, type: String

  # -- Sorting
  # Downcase version of the name suitable for sorting. Format of name sort
  # is <name><email>
  field :name_sort, type: String

  # -- Core Data Fields
  field :known_as, type: String # Used for email communications, etc.
  field :date_of_birth, type: Date

  # Locale selection for this user, language being the primary driver here.
  # A "nil" value means no preference has been supplied and the Account/Survey default
  # will apply.
  # See also: http://rdoc.info/github/svenfuchs/i18n/I18n/Locale
  field :locale, type: String

  # Default Time Zone for this user.
  # Should use IANA standard zones http://en.wikipedia.org/wiki/List_of_tz_database_time_zones
  # A "nil" value means no preference has been supplied and the Account/Survey default
  # will apply.
  # See also: http://api.rubyonrails.org/classes/ActiveSupport/TimeZone.html
  field :time_zone, type: String
  field :mobile_phone_number, type: String

  field :event_employee_id, type: String

  field :terms_and_conditions_accepted, default: false

  field :company_role, type: String

  # -- Indexes

  index(email: 1, name: 1)
  index(account_id: 1, name_sort: 1)
  index({employee_id: 1}, {background: true})

  index(account_id: 1, email: 1)
  index(account_id: 1, employee_id: 1)
  index({remember_token: 1}, {background: true})
  index({reset_password_token: 1}, {background: true})
  index({created_at: 1}, {background: true}) # For the murmur event log
  index({name_locked: 1}, {background: true})

  # For SRE
  index({updated_at: 1}, {background: true})
  index({active_until: 1}, {background: true})

  # -- Validations

  validates :name, presence: true
  validates :name, length: {maximum: 255}
  validates :locale, inclusion: {in: I18n.available_locales.map(&:to_s), allow_nil: true}
  validate :must_be_at_least_one_year_old

  # Override Devise's email uniqueness validation by:
  # Disabling Devise's built-in email validation check
  def email_changed?
    false # This prevents Devise from running its uniqueness validation
  end

  def will_save_change_to_email?
    false # Also prevents Devise validation from running
  end

  # Add our own validation for email uniqueness scoped to account
  validate :email_uniqueness_within_account, if: :will_manually_validate_email?

  # Only run our validation when email is actually changing
  def will_manually_validate_email?
    (new_record? || changes.key?("email")) && email.present?
  end

  def email_uniqueness_within_account
    return unless email.present? && account_id.present?

    # Find any existing person with the same email in the same account
    existing_person = Person.where(
      :account_id => account_id,
      :email => email,
      :_id.ne => id
    ).first

    if existing_person
      errors.add(:email, "is already taken by another user in this account")
    end
  end

  def must_be_at_least_one_year_old
    return if date_of_birth.blank?

    errors.add(:date_of_birth, "must be at least 1 year ago") if date_of_birth > Date.today - 1.year
  end

  scope :with_search_term, ->(term) { where(name_sort: /#{::Regexp.escape(term)}/i) }
  scope :with_locked_names, -> { where(name_locked: true) }
  scope :emailable, -> { where(:email.ne => nil) }

  before_validation do
    # Make sure we're not getting crufty locale data
    self.locale = locale.blank? ? nil : locale.to_s
  end

  # Downcase email assignment
  # Name sort includes the email, name and employee_id, so it can
  # - be used for fuzzy/name sorting
  # - be used to sort users with the same name consistently
  # NB For the name_sort field we are using a class method. This is a bit goofy
  #    but it's necessary because we use that method from import scripts and so
  #    on where we bypass Mongoid for performance reasons.
  before_save do
    self.email = email.try(:downcase)
    self.name_sort = Person.generate_name_sort(name, email, employee_id)
  end

  # @param [ Array<String> ] candidates List of locales to select from, in order of priority.
  # @param [ Array<String>, String ] fallbacks A single or array of fallback
  # locales for this user. For example, this might be a browser-supplied locales.
  # @return [ String ] the preferred locale for this user.
  def select_preferred_locale(candidates, fallbacks = nil)
    # Try the user's default, then scan the fallbacks
    selected = locale.present? && candidates.include?(locale.to_s) ? locale : nil

    if selected.nil? && fallbacks.present?
      fallbacks = [fallbacks] unless fallbacks.is_a?(Array)
      selected = fallbacks.detect { |l| candidates.include?(l) }
    end

    # Either we've got a hit, or use the top priority locale
    selected || candidates.first
  end

  def demographic_value(demographic_id)
    @_demographic_value_assignment_hash ||= demographic_value_assignments.index_by(&:demographic_id)
    @_demographic_value_assignment_hash[demographic_id]&.demographic_value_id
  end

  def demographic_value_label(demographic_id)
    demographic = Question.where(aggregate_id: demographic_id).first
    demographic.demographic_label_for_id(demographic_value(demographic_id))
  end

  def demographic_value_value(demographic_id)
    demographic = Question.where(aggregate_id: demographic_id).first
    demographic.demographic_value_for_id(demographic_value(demographic_id))
  end

  def set_demographic_value!(demographic_id, demographic_value_id)
    if demographic_value_id.blank?
      unset_demographic_value!(demographic_id)
    else
      demographic_value_assignments
        .find_or_initialize_by(demographic_id: demographic_id)
        .update(demographic_value_id: demographic_value_id)
    end
    self[:updated_at] = Time.now.utc
    save!
  end

  # replaces all existing assignments
  # demographic_value_assignments format: [{demographic_id:, demographic_value_id: },..]
  def set_demographic_values!(demographic_value_assignments)
    assignments = demographic_value_assignments.select { |assignment|
      assignment[:demographic_id].present? && assignment[:demographic_value_id].present?
    }
    set(demographic_value_assignments: assignments, updated_at: Time.now.utc)
  end

  def accept_terms_and_conditions!
    update_attributes!(terms_and_conditions_accepted: true)
  end

  # adds on top of existing assignments
  # demographic_value_assignments format: [{demographic_id:, demographic_value_id: },..]
  def add_demographic_values!(new_assignments)
    assignments_map = demographic_value_assignments.each_with_object({}) { |assignment, result|
      result[assignment[:demographic_id]] = assignment[:demographic_value_id]
    }
    new_assignments.each do |assignment|
      assignments_map[assignment[:demographic_id]] = assignment[:demographic_value_id]
    end
    assignments = []
    assignments_map.each_pair do |demographic_id, demographic_value_id|
      next unless demographic_id.present? && demographic_value_id.present?

      assignments << DemographicValueAssignment.build_doc(demographic_id, demographic_value_id)
    end
    set(demographic_value_assignments: assignments, updated_at: Time.now.utc)
  end

  def remove_demographic_assignments(demographic_id, demographic_values_ids)
    demographic_value_assignments.delete_all(:demographic_id => demographic_id, :demographic_value_id.in => demographic_values_ids)
  end

  def calculate_demographic_value_id(demographic_question: question, date_to_use: date)
    if demographic_question.core_data_type.nil?
      # Not a core demographic question, just grab the value on the employee
      demographic_value(demographic_question.aggregate_id)
    else
      value = if demographic_question.core_data_type == :age
        age(date_to_use)
      elsif demographic_question.core_data_type == :tenure
        tenure(date_to_use)
      end
      demographic_question.match_core_data_option(value)&.demographic_value_id
    end
  end

  def identifier
    employee_id || email
  end

  def email_or_name
    return email if email.present?

    name
  end

  # In the long term, we should move this to the validations and generate ids
  # for all users who do not already have an email. (See PROD-2801)
  def has_valid_identifier?
    is_valid = employee_id.present? || email.present?
    errors.add(:email, "Can't be blank without an employee id") unless is_valid
    is_valid
  end

  # Used to identify this user in audit logs, etc
  def to_s
    s = name
    s += " (#{identifier})" unless identifier.nil?
    s += " of account #{account.name}" unless account.nil?
    s
  end

  def emailable?
    email.present?
  end

  def send_notifications?
    emailable?
  end

  # This will persist the demographic_value_assignments if the parent is persisted. If you want to persist the parent
  # as well, you can call `update_core_data!`
  def update_core_data(core_data_questions: CoreDemographics.questions)
    assignments = core_data_questions.map { |demographic|
      demographic_value = match_core_data_option_for(demographic)
      {demographic_id: demographic.aggregate_id, demographic_value_id: demographic_value.demographic_value_id}
    }
    add_demographic_values!(assignments)
  end

  def update_core_data!(validate: true)
    update_core_data
    save!(validate: validate)
  end

  def age(today = Time.now.utc.to_date)
    Person.age(date_of_birth, today)
  end

  def display_name
    preferred_name.presence || name
  end

  def name_locked
    self[:name_locked].presence || false
  end

  # -- Class Methods
  class << self
    def email(address)
      return nil if address.blank?

      where(email: address.downcase.strip).first
    end

    def generate_name_sort(name, email, employee_id)
      "#{name}_#{email}_#{employee_id}".downcase
    end

    def build_age_assignment_as_doc(master_survey, birth_date)
      return unless master_survey

      age_question = CoreDemographics.age_question
      return unless master_survey.question_in_survey(age_question)

      demographic_id = age_question.aggregate_id
      demographic_value = age_question.match_core_data_option(Person.age(birth_date))
      DemographicValueAssignment.build_doc(demographic_id, demographic_value&.demographic_value_id)
    end

    def age(date_of_birth, today = Time.now.utc.to_date)
      return nil if date_of_birth.blank?

      age = today.year - date_of_birth.year
      age -= 1 unless had_birthday_this_year?(date_of_birth, today)
      age
    end

    def find_employee_by_employee_id(subdomain, employee_id)
      account = Account.subdomain(subdomain)
      account.people.where(employee_id: employee_id).first_by_id if account
    end

    def find_employee_by_identifier_and_subdomain(identifier, subdomain)
      account = Account.subdomain(subdomain)
      if account
        account.people.any_of(
          {email: identifier.downcase.strip},
          {id: identifier.strip}
        ).first_by_id
      end
    end

    def find_employees_by_identifier(identifier)
      ::Person.any_of(
        {email: identifier.downcase.strip},
        {id: identifier.strip}
      )
    end

    def find_employee_by_name(name, excluded_account = nil)
      people = ::Person.active.where(name: /#{Regexp.escape(name)}/i)
      people = people.where(:account.ne => excluded_account) if excluded_account
      people.first_by_id
    end

    def find_employee_by_aggregate_id(aggregate_id)
      ::Person.where(aggregate_id: aggregate_id.strip).first
    end

    private

    def had_birthday_this_year?(date_of_birth, today = Time.now.utc.to_date)
      today.month > date_of_birth.month ||
        (today.month == date_of_birth.month && today.day >= date_of_birth.day)
    end
  end

  def secondary_flag_source
    account
  end

  private

  def match_core_data_option_for(demographic)
    case demographic.core_data_type
    when :age
      demographic.match_core_data_option(age)
    when :tenure
      demographic.match_core_data_option(tenure_in_months)
    end
  end

  def unset_demographic_value!(demographic_id)
    demographic_value_assignments.delete_all(demographic_id: demographic_id)
  end
end
