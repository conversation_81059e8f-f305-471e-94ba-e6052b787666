require "slack/web_api_factory"

module Slackable
  extend ActiveSupport::Concern
  # Keep in mind this module will be included in Person.

  included do
    field :slack_id, type: String
    field :slack_name, type: String

    index({slack_id: 1}, {background: true})

    scope :slackable, -> { ne(slack_id: nil) }
    scope :unslackable, -> { where(slack_id: nil) }

    before_update do
      if changes.key?("email")
        self.slack_id = nil
        self.slack_name = nil
      end
    end

    def self.unslack_everybody(account:)
      account.people.slackable.update_all(slack_id: nil, slack_name: nil)
    end

    def self.sync_member(account, member_data)
      slacker = ::Slack::User.new(member_data)
      return false unless slacker.real?

      user = account.people.employees.active.where(email: slacker.email).first
      return false unless user

      user.set_slack_attributes(slack_id: slacker.id, slack_name: slacker.name)
      true
    end
  end

  def slackable?
    slack_id.present?
  end

  def set_slack_attributes(slack_id:, slack_name:)
    update_attributes!(slack_id: slack_id, slack_name: slack_name)
  end
end
